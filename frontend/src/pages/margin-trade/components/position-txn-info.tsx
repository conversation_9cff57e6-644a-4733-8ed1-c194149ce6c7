import { useMemo, useState } from 'react'
import { MarginTradePositionTxnInfoTabEnum } from '../types'
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useMyPositionColumns } from '../hooks/use-myposition-columns'
import { DataTable } from '@/components/data-table'
import { getIllustrationUrl } from '@/lib/assets'
import { cn } from '@/lib/utils'
import {
  LeverageOperationType,
  type LeverageObligation
} from '@pebble-protocol/pebble-sdk'
import { useMarketInfo } from '@/hooks/use-market'
import { Transactions } from './transactions'

export const PositionTxnInfo = ({
  marketName,
  positions,
  isLoading
}: {
  marketName: string
  positions?: LeverageObligation[]
  isLoading: boolean
}) => {
  const marketInfo = useMarketInfo(marketName)
  const noPosition = !positions || positions.length === 0
  const [tab, setTab] = useState(MarginTradePositionTxnInfoTabEnum.Position)

  const { columns: myPositionColumns } = useMyPositionColumns()

  const myPositionData = useMemo(() => {
    // console.log('positions', positions?.length)
    // console.log('marketInfo', marketInfo)
    return positions
      ? positions.map((position, idx) => {
          // console.log('position', position)
          const pnlPercentage = marketInfo
            ? `${position.pnl(marketInfo.market).mulBigInt(100n).divDecimal(position.principleUSD(marketInfo.market)).asNumber().toFixed(2)}%`
            : '-'
          return {
            id: `pos-${idx}`,
            marketName: marketName,
            leftAsset: position.leverageObligation.info?.deposit ?? '',
            rightAsset: position.leverageObligation.info?.borrow ?? '',
            leverage: Number(position.leverage().asNumber().toFixed(2)),
            leverageType:
              position.operationType() === LeverageOperationType.SwapBorrow
                ? 'Short'
                : 'Long',
            size: marketInfo
              ? `$${position.leverageSizeUSD(marketInfo.market).asNumber().toFixed(2)}`
              : '-', //error: marketInfo? position.leverageSizeUSD(marketInfo.market).toString(): '0',
            pnl1: marketInfo
              ? `$${position.pnl(marketInfo.market).asNumber().toFixed(2)}`
              : '-', // TODO
            pnl2: pnlPercentage, // TODO
            entryPrice: position.averagePrice().toString(),
            collateralLiqPrice: marketInfo
              ? position
                  .collateralLiquidationPrice(marketInfo.market)
                  .toString()
              : '0',
            debtLiqPrice: marketInfo
              ? position.debtLiquidationPrice(marketInfo.market).toString()
              : '0',
            hash: '0x1234567890'
          }
        })
      : []
  }, [positions, marketInfo, marketName])

  return (
    <div
      style={{ height: noPosition ? 272 : 'fit-content' }}
      className="border relative overflow-hidden border-border-8 rounded-[10px] bg-vault-info-gradient-2 flex flex-col">
      <div className="px-6 z-10 border-b border-border-5">
        <Tabs
          value={tab}
          onValueChange={(value) =>
            setTab(value as MarginTradePositionTxnInfoTabEnum)
          }
          orientation="vertical">
          <TabsList
            className="pl-0 border-none"
            aria-label="position txn info tabs">
            <TabsTrigger value={MarginTradePositionTxnInfoTabEnum.Position}>
              {MarginTradePositionTxnInfoTabEnum.Position}
            </TabsTrigger>
            <TabsTrigger value={MarginTradePositionTxnInfoTabEnum.Txn}>
              {MarginTradePositionTxnInfoTabEnum.Txn}
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      <div
        className={cn(
          'px-8 py-3 z-20 overflow-y-auto relative',
          tab === MarginTradePositionTxnInfoTabEnum.Txn && 'max-h-[225px]'
        )}>
        {tab === MarginTradePositionTxnInfoTabEnum.Position ? (
          noPosition ? (
            <div className="min-h-[200px] flex-1 size-full flex items-center justify-center">
              <div className="flex items-center gap-x-4 justify-between">
                <div className="h-[1px] w-[52px] bg-foreground opacity-10" />
                <span className="opacity-30 text-center text-sm">
                  You have no position yet
                </span>
                <div className="h-[1px] w-[52px] bg-foreground opacity-10" />
              </div>
            </div>
          ) : (
            <DataTable
              data={myPositionData}
              columns={myPositionColumns}
              isLoading={isLoading}
              classNames={{
                headerCell: 'px-3',
                contentCell: 'px-3'
              }}
              size="md"
            />
          )
        ) : (
          <Transactions />
        )}
      </div>
      <img
        src={getIllustrationUrl('charts-info-bg-layer', 'svg')}
        alt="charts-info-bg-layer"
        className="absolute top-[127px] left-1/2 -translate-1/2 w-[644px] h-[504px] object-contain bg-no-repeat size-full z-0 no-drag opacity-40 pointer-events-none"
      />
    </div>
  )
}
