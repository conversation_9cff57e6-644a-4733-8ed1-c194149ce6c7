import { Fragment, useMemo } from 'react'
import { cn } from '@/lib/utils'
import { TokenPairSelect } from './token-pair-select'
import { ChevronUpIcon } from '@/assets/icons'
import type { IMarginTradeItem } from '@/types/margin-trade'
import useMarketTokenInfo from '@/pages/market/hooks/use-market-token-info'
import type { Token } from '@/components/balance-input'
import { NumberCell } from '@/components/number-cell'
import { useLeveragePrice } from '../hooks/use-leverage-price'

export function Overview({
  tokenPairList,
  selectedItem,
  onSelectChange,
  selectedToken
}: {
  tokenPairList: IMarginTradeItem[]
  selectedItem?: IMarginTradeItem | undefined
  onSelectChange: (value: string) => void
  selectedToken: Token | undefined
}) {
  const marketName = useMemo(() => {
    return selectedItem?.fromMarket ?? ''
  }, [selectedItem])
  const tokenAddress0 = useMemo(() => {
    return selectedItem?.tokenInfo0.address ?? ''
  }, [selectedItem])
  const tokenAddress1 = useMemo(() => {
    return selectedItem?.tokenInfo1.address ?? ''
  }, [selectedItem])
  const { marketTokenInfo: tokenInfo0 } = useMarketTokenInfo(
    marketName,
    tokenAddress0
  )
  const { marketTokenInfo: tokenInfo1 } = useMarketTokenInfo(
    marketName,
    tokenAddress1
  )

  const token0BorrowApy = useMemo(() => {
    if (!tokenInfo0) return 0
    return Number(Number(tokenInfo0.borrowAPY).toFixed(4))
  }, [tokenInfo0])

  const token0SupplyApy = useMemo(() => {
    if (!tokenInfo0) return 0
    return Number(Number(tokenInfo0.supplyAPY).toFixed(4))
  }, [tokenInfo0])
  const token1BorrowApy = useMemo(() => {
    if (!tokenInfo1) return 0
    return Number(Number(tokenInfo1.borrowAPY).toFixed(4))
  }, [tokenInfo1])
  const token1SupplyApy = useMemo(() => {
    if (!tokenInfo1) return 0
    return Number(Number(tokenInfo1.supplyAPY).toFixed(4))
  }, [tokenInfo1])

  const supplyAndBorrowInfo = useMemo(() => {
    if (!selectedItem) return undefined

    if (
      selectedToken?.coinType.replace('0x', '').toLowerCase() ===
      selectedItem?.tokenInfo0.address.replace('0x', '').toLowerCase()
    ) {
      return {
        supplySymbol: selectedItem?.tokenInfo0.symbol,
        supplyApy: token0SupplyApy,
        borrowSymbol: selectedItem?.tokenInfo1.symbol,
        borrowApy: token1BorrowApy
      }
    } else {
      return {
        supplySymbol: selectedItem?.tokenInfo1.symbol,
        supplyApy: token1SupplyApy,
        borrowSymbol: selectedItem?.tokenInfo0.symbol,
        borrowApy: token0BorrowApy
      }
    }
  }, [
    selectedItem,
    selectedToken,
    token0BorrowApy,
    token0SupplyApy,
    token1BorrowApy,
    token1SupplyApy
  ])

  // 构建交易对标识符，用于价格查询
  const pairSymbol = useMemo(() => {
    if (!selectedItem?.tokenInfo0.symbol || !selectedItem?.tokenInfo1.symbol) {
      return ''
    }
    return selectedItem.tokenInfo0.symbol + selectedItem.tokenInfo1.symbol
  }, [selectedItem?.tokenInfo0.symbol, selectedItem?.tokenInfo1.symbol])

  // 只有当交易对标识符存在时才发起价格查询
  const { currentPrice, priceChangePercent24h } = useLeveragePrice(pairSymbol, {
    refetchInterval: 60000,
    enabled: !!pairSymbol // 只有当 pairSymbol 有值时才启用查询
  })
  // debugger

  const overviewData = useMemo(
    () => [
      {
        id: 'pair',
        label: 'Pair',
        value:
          selectedItem?.tokenInfo0.symbol +
          '/' +
          selectedItem?.tokenInfo1.symbol,
        renderCell: () =>
          selectedItem && (
            <TokenPairSelect
              tokenPairList={tokenPairList}
              selectedItem={selectedItem}
              onSelectChange={onSelectChange}
            />
          )
      },
      {
        id: 'price',
        label: 'price',
        value: 'pecent',
        renderCell: () => (
          <div className="flex flex-col">
            <span className="text-base">{Number(currentPrice).toFixed(2)}</span>
            <span
              className={`flex items-center gap-x-1.5 ${
                priceChangePercent24h >= 0 ? 'text-green' : 'text-red'
              }`}>
              {priceChangePercent24h >= 0 ? (
                <ChevronUpIcon />
              ) : (
                <ChevronUpIcon className="rotate-180" />
              )}
              {Math.abs(Number(priceChangePercent24h)).toFixed(2)}%
            </span>
          </div>
        )
      },
      {
        id: 'longLiquidity',
        label: 'Long Liquidity',
        value: <NumberCell value={selectedItem?.longLiquidity} />
      },
      {
        id: 'shortLiquidity',
        label: 'Short Liquidity',
        value: <NumberCell value={selectedItem?.shortLiquidity} />
      },
      {
        id: 'supplyAPY',
        label: `${supplyAndBorrowInfo?.supplySymbol} Supply APY`,
        value: (
          <span className="text-green">
            {!!supplyAndBorrowInfo && supplyAndBorrowInfo?.supplyApy !== null
              ? `${(supplyAndBorrowInfo.supplyApy * 100).toFixed(2)}%`
              : '-'}
          </span>
        )
      },
      {
        id: 'borrowAPY',
        label: `${supplyAndBorrowInfo?.borrowSymbol} Borrow APY`,
        value: (
          <span className="text-primary">
            {!!supplyAndBorrowInfo && supplyAndBorrowInfo?.borrowApy !== null
              ? `${(supplyAndBorrowInfo?.borrowApy * 100).toFixed(2)}%`
              : '-'}
          </span>
        )
      }
    ],
    [
      onSelectChange,
      selectedItem,
      supplyAndBorrowInfo,
      tokenPairList,
      currentPrice,
      priceChangePercent24h
    ]
  )
  // debugger;

  return (
    <div className="border w-[936px] border-border-8 rounded-[10px] overflow-hidden flex justify-between items-center bg-overview-gradient py-3 self-stretch">
      {overviewData.map((item, index) => (
        <Fragment key={item.id}>
          {index === 0 && (
            <div className="h-[66px] w-[1px] bg-border-8 invisible" />
          )}
          {item.renderCell ? (
            item.renderCell()
          ) : (
            <div className="flex flex-col items-center justify-center gap-y-1.5 py-3">
              <div className="text-xs opacity-60 text-nowrap">{item.label}</div>
              <div>{item.value}</div>
            </div>
          )}
          <div
            className={cn(
              'h-[66px] w-[1px] bg-border-8',
              index === overviewData.length - 1 && 'invisible'
            )}
          />
        </Fragment>
      ))}
    </div>
  )
}
